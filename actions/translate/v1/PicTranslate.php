<?php
/**
 * @befie 图片翻译接口
 * @file PicTranslate.php
 * <AUTHOR> Assistant
 * @version 1.0
 * @date 2025-01-29
 */
class Action_PicTranslate extends Kdproduct_Action_MisAuthBase
{
    public function invoke()
    {
        $arrInput = [
            'image' => isset($this->_requestParam['image']) ? strval($this->_requestParam['image']) : '',
            'transFrom' => isset($this->_requestParam['transFrom']) ? strval($this->_requestParam['transFrom']) : '',
            'transTo' => isset($this->_requestParam['transTo']) ? strval($this->_requestParam['transTo']) : '',
        ];

        // 参数验证
        if (empty($arrInput['image'])) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'image参数不能为空');
        }
        if (empty($arrInput['transFrom'])) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'transFrom参数不能为空');
        }
        if (empty($arrInput['transTo'])) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::PARAM_ERROR, 'transTo参数不能为空');
        }

        Hk_Util_Log::start('misresprod_pictranslate');
        
        // 调用远程服务
        $result = Service_Data_Rpc_MisResProdRpc::pictranslate(
            $arrInput['image'],
            $arrInput['transFrom'],
            $arrInput['transTo']
        );
        
        Hk_Util_Log::stop('misresprod_pictranslate');

        if ($result === false) {
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::SYSTEM_CRAZY, '图片翻译服务调用失败');
        }

        return $result;
    }
}
